{"name": "queue-mgmt-api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.16.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "nodemon": "^3.1.10"}, "devDependencies": {"prisma": "^6.16.2"}}