const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
const dotenv = require("dotenv");
const { PrismaClient } = require("@prisma/client");

dotenv.config();
const app = express();
app.use(cors());
app.use(bodyParser.json());

const prisma = new PrismaClient();

// ------------------- QUEUE APIs ------------------- //

// Get all tokens
app.get("/queue", async (req, res) => {
  const data = await prisma.queue.findMany({ orderBy: { id: "asc" } });
  res.json(data);
});

// Add new token
app.post("/queue", async (req, res) => {
  const { name } = req.body;
  const data = await prisma.queue.create({ data: { name } });
  res.json(data);
});

// Update token status
app.put("/queue/:id", async (req, res) => {
  const { status } = req.body;
  const data = await prisma.queue.update({
    where: { id: Number(req.params.id) },
    data: { status },
  });
  res.json(data);
});

// Delete token
app.delete("/queue/:id", async (req, res) => {
  await prisma.queue.delete({ where: { id: Number(req.params.id) } });
  res.json({ message: "Deleted" });
});

// --------------------------------------------------- //

app.listen(process.env.PORT || 4000, () =>
  console.log(`🚀 API running at http://localhost:${process.env.PORT || 4000}`)
);
